.header {
  width: 100%;
  position: fixed;
  top: 0;
  z-index: 1000;
}

.headerContainer {
  width: 1200px;
  /* outline: 1px solid red; */
  height: 100%;
  margin: 0 auto;
  display: flex;
  align-items: center;

}

.logoContainer {
  width: 150px;
  height: 100%;
  /* outline: 1px solid red; */
  display: flex;
  align-items: center;
  cursor: pointer;
}

.logo {
  width: 100%;
  height: 40px;
  /* outline: 1px solid red; */
  background: url("../../public/logo1.png");
  background-size: contain;
  background-position: center;
  transform: scale(1.3);
}

.navContainer {
  width: 400px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  /* outline: 1px solid greenyellow; */
}

.navgation {
  color: #fff;
  font-size: 16px;
}



.searchContainer {
  width: 500px;
  height: 100%;
  display: flex;
  align-items: center;
}


.loginBtnContainer {
  width: 200px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  /* outline: 1px solid blue; */
}

.content {
  width: 1200px;
  /* height: 5000px; */
  background-color: #fff;
  margin: 85px auto 0 auto;
}

.footer {
  width: 100vw;
  /* outline: 1px solid red; */
}

.footer p {
  text-align: center;
  color: rgb(102, 102, 102);
  margin: 0 auto;
}

.footer .links {
  width: 500px;
  margin: 0 auto;
  display: flex;
  justify-content: center;
}

.footer .linkItem {
  margin-right: 10px;
}


.paginationContainer{
  width: 100%;
  height: 80px;
  display: flex;
  justify-content: center;
  align-items:center;
  /* outline: 1px solid blue; */
}

